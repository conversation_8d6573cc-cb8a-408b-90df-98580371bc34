#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module de Recherche dans la Mémoire Persistante de Roony
========================================================

Ce module permet de rechercher des cas similaires dans la base
de connaissances vectorielle locale pour enrichir le contexte
des nouvelles requêtes utilisateur.

ATTENTION : Module critique pour l'injection de contexte dynamique.
"""

import chromadb
import json
import os
from sentence_transformers import SentenceTransformer
from pathlib import Path
import logging
from typing import List, Dict, Any, Tuple, Optional

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MemoireSearcher:
    """
    Chercheur dans la mémoire persistante de Roony
    
    Responsabilités:
    - Vectorisation des nouvelles questions utilisateur
    - Recherche sémantique dans ChromaDB
    - Retour des cas les plus pertinents
    """
    
    def __init__(self, memoire_dir: str = "./memoire", chroma_dir: str = "./chroma_db"):
        """
        Initialise le chercheur avec les mêmes paramètres que l'indexeur
        
        Args:
            memoire_dir: Répertoire contenant les fichiers JSON des cas
            chroma_dir: Répertoire de la base ChromaDB locale
        """
        self.memoire_dir = Path(memoire_dir)
        self.chroma_dir = Path(chroma_dir)
        
        logger.info(f"Initialisation du chercheur de mémoire")
        logger.info(f"Répertoire mémoire: {self.memoire_dir}")
        logger.info(f"Base ChromaDB: {self.chroma_dir}")
        
        # Vérifications critiques
        if not self.chroma_dir.exists():
            raise FileNotFoundError(f"Base ChromaDB introuvable: {self.chroma_dir}")
            
        if not self.memoire_dir.exists():
            raise FileNotFoundError(f"Répertoire mémoire introuvable: {self.memoire_dir}")
        
        # Initialisation des clients - MÊMES PARAMÈTRES que l'indexeur
        try:
            self.client = chromadb.PersistentClient(path=str(self.chroma_dir))
            logger.info("✅ Client ChromaDB connecté")
        except Exception as e:
            logger.error(f"❌ Erreur connexion ChromaDB: {e}")
            raise
            
        try:
            # MÊME MODÈLE que l'indexeur - CRITIQUE pour la cohérence
            self.model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
            logger.info("✅ Modèle sentence-transformers chargé")
        except Exception as e:
            logger.error(f"❌ Erreur chargement modèle: {e}")
            raise
            
        # Récupération de la collection
        try:
            self.collection = self.client.get_collection(name="memoire_roony")
            logger.info("✅ Collection 'memoire_roony' trouvée")
            
            # Vérification du contenu
            count = self.collection.count()
            logger.info(f"📊 Collection contient {count} cas indexés")
            
            if count == 0:
                logger.warning("⚠️ Aucun cas trouvé - exécutez d'abord indexer_memoire.py")
                
        except Exception as e:
            logger.error(f"❌ Collection 'memoire_roony' introuvable: {e}")
            logger.error("💡 Conseil: Exécutez d'abord indexer_memoire.py")
            raise
    
    def chercher_cas_similaires(self, question_utilisateur: str, top_n: int = 2) -> List[str]:
        """
        Fonction principale de recherche sémantique
        
        Args:
            question_utilisateur: Question ou problème de l'utilisateur
            top_n: Nombre maximum de cas similaires à retourner
            
        Returns:
            List[str]: Liste des IDs des cas les plus pertinents
        """
        try:
            logger.info(f"🔍 Recherche pour: '{question_utilisateur[:100]}...'")
            logger.info(f"📊 Top {top_n} cas demandés")
            
            # Validation des entrées
            if not question_utilisateur or not question_utilisateur.strip():
                logger.warning("⚠️ Question vide - aucun résultat")
                return []
                
            if top_n <= 0:
                logger.warning("⚠️ top_n invalide - aucun résultat")
                return []
            
            # Vectorisation de la question avec le MÊME MODÈLE
            try:
                question_embedding = self.model.encode(question_utilisateur.strip()).tolist()
                logger.debug(f"✅ Question vectorisée: {len(question_embedding)} dimensions")
            except Exception as e:
                logger.error(f"❌ Erreur vectorisation question: {e}")
                return []
            
            # Recherche dans ChromaDB
            try:
                results = self.collection.query(
                    query_embeddings=[question_embedding],
                    n_results=top_n
                )
                
                # Extraction des IDs
                cas_ids = results['ids'][0] if results['ids'] else []
                distances = results['distances'][0] if results['distances'] else []
                
                logger.info(f"✅ {len(cas_ids)} cas trouvés")
                
                # Affichage des résultats avec scores de similarité
                for i, (cas_id, distance) in enumerate(zip(cas_ids, distances)):
                    similarite = 1 - distance  # Convertir distance en similarité
                    logger.info(f"  {i+1}. {cas_id} (similarité: {similarite:.3f})")
                
                return cas_ids
                
            except Exception as e:
                logger.error(f"❌ Erreur recherche ChromaDB: {e}")
                return []
                
        except Exception as e:
            logger.error(f"❌ Erreur générale lors de la recherche: {e}")
            return []
    
    def charger_details_cas(self, cas_ids: List[str]) -> List[Dict[str, Any]]:
        """
        Charge les détails complets des cas depuis les fichiers JSON
        
        Args:
            cas_ids: Liste des IDs de cas à charger
            
        Returns:
            List[Dict]: Liste des données complètes des cas
        """
        cas_details = []
        
        for cas_id in cas_ids:
            try:
                # Construction du chemin de fichier
                fichier_path = self.memoire_dir / f"{cas_id}.json"
                
                if not fichier_path.exists():
                    logger.warning(f"⚠️ Fichier introuvable: {fichier_path}")
                    continue
                
                # Chargement des données
                with open(fichier_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                cas_details.append(data)
                logger.debug(f"✅ Détails chargés pour: {cas_id}")
                
            except Exception as e:
                logger.error(f"❌ Erreur chargement {cas_id}: {e}")
                continue
        
        logger.info(f"📋 {len(cas_details)} cas détaillés chargés")
        return cas_details
    
    def rechercher_avec_details(self, question_utilisateur: str, top_n: int = 2) -> Dict[str, Any]:
        """
        Recherche complète avec détails des cas trouvés
        
        Args:
            question_utilisateur: Question de l'utilisateur
            top_n: Nombre de cas à retourner
            
        Returns:
            Dict contenant les IDs et les détails complets
        """
        # Recherche des IDs
        cas_ids = self.chercher_cas_similaires(question_utilisateur, top_n)
        
        if not cas_ids:
            return {
                "cas_ids": [],
                "cas_details": [],
                "nombre_resultats": 0,
                "question_originale": question_utilisateur
            }
        
        # Chargement des détails
        cas_details = self.charger_details_cas(cas_ids)
        
        return {
            "cas_ids": cas_ids,
            "cas_details": cas_details,
            "nombre_resultats": len(cas_details),
            "question_originale": question_utilisateur
        }
    
    def formater_contexte_memoire(self, cas_details: List[Dict[str, Any]]) -> str:
        """
        Formate les cas trouvés en contexte textuel pour injection dans le prompt
        
        Args:
            cas_details: Liste des détails des cas pertinents
            
        Returns:
            str: Contexte formaté prêt pour injection
        """
        if not cas_details:
            return ""
        
        contexte_parts = []
        contexte_parts.append("--- MÉMOIRE TAMPON : EXEMPLES DE CAS PRÉCÉDENTS RÉSOLUS AVEC SUCCÈS ---")
        
        for i, cas in enumerate(cas_details, 1):
            contexte_parts.append(f"\\n**Cas pertinent #{i} : {cas.get('problem_summary', 'Problème non défini')}**")
            
            # Points clés de la solution
            if 'solution_points_cles' in cas and cas['solution_points_cles']:
                points_cles = '. '.join(cas['solution_points_cles'])
                contexte_parts.append(f"- Points clés de la solution : {points_cles}")
            
            # Contraintes respectées
            if 'user_constraints' in cas and cas['user_constraints']:
                contraintes = ', '.join(cas['user_constraints'])
                contexte_parts.append(f"- Contraintes respectées : {contraintes}")
            
            # Leçons apprises si disponibles
            if 'lecons_apprises' in cas and cas['lecons_apprises']:
                lecons = '. '.join(cas['lecons_apprises'])
                contexte_parts.append(f"- Leçons apprises : {lecons}")
            
            # Résultats si disponibles
            if 'resultats_mesures' in cas:
                resultats = cas['resultats_mesures']
                satisfaction = resultats.get('satisfaction_utilisateur', 'N/A')
                performance = resultats.get('performance_technique', 'N/A')
                contexte_parts.append(f"- Résultats : Satisfaction {satisfaction}%, Performance {performance}%")
        
        contexte_parts.append("---\\n")
        
        return "\\n".join(contexte_parts)
    
    def recherche_complete_pour_injection(self, question_utilisateur: str, top_n: int = 2) -> str:
        """
        Fonction tout-en-un pour obtenir le contexte mémoire formaté
        prêt à être injecté dans un prompt
        
        Args:
            question_utilisateur: Question de l'utilisateur
            top_n: Nombre de cas à rechercher
            
        Returns:
            str: Contexte mémoire formaté ou chaîne vide si aucun résultat
        """
        try:
            # Recherche avec détails
            resultats = self.rechercher_avec_details(question_utilisateur, top_n)
            
            if resultats["nombre_resultats"] == 0:
                logger.info("ℹ️ Aucun cas pertinent trouvé")
                return ""
            
            # Formatage pour injection
            contexte_formate = self.formater_contexte_memoire(resultats["cas_details"])
            
            logger.info(f"✅ Contexte mémoire généré ({len(contexte_formate)} caractères)")
            return contexte_formate
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la recherche complète: {e}")
            return ""


# Fonction standalone pour compatibilité avec l'intégration
def chercher_cas_similaires(question_utilisateur: str, top_n: int = 2) -> List[str]:
    """
    Fonction standalone pour recherche rapide d'IDs de cas
    Compatible avec l'exemple d'intégration fourni dans les spécifications
    
    Args:
        question_utilisateur: Question de l'utilisateur
        top_n: Nombre de cas à retourner
        
    Returns:
        List[str]: IDs des cas les plus pertinents
    """
    try:
        searcher = MemoireSearcher()
        return searcher.chercher_cas_similaires(question_utilisateur, top_n)
    except Exception as e:
        logger.error(f"❌ Erreur fonction standalone: {e}")
        return []


def main():
    """
    Test du module de recherche
    """
    try:
        logger.info("🔍 ROONY - Module de Recherche Mémoire v1.0")
        logger.info("=" * 50)
        
        # Test de recherche
        searcher = MemoireSearcher()
        
        # Question de test
        question_test = "J'ai besoin d'optimiser les performances de mon système agentique"
        
        logger.info(f"🧪 Test avec la question: '{question_test}'")
        
        # Recherche complète
        contexte = searcher.recherche_complete_pour_injection(question_test, 2)
        
        if contexte:
            print("\\n" + "="*60)
            print("CONTEXTE MÉMOIRE GÉNÉRÉ:")
            print("="*60)
            print(contexte)
            print("="*60)
        else:
            print("Aucun contexte mémoire trouvé")
            
    except Exception as e:
        logger.error(f"💥 ERREUR: {e}")


if __name__ == "__main__":
    main()
