../../Scripts/jsonschema.exe,sha256=EGkFJCB2tPkNKNJ-fmKr64rhYg2RsAVXy9B4B1EWUCI,108399
jsonschema-4.25.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
jsonschema-4.25.1.dist-info/METADATA,sha256=Mfg8FXnkbhr2ImnO-l2DU07xVfPuIZFPeIG71US8Elw,7608
jsonschema-4.25.1.dist-info/RECORD,,
jsonschema-4.25.1.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
jsonschema-4.25.1.dist-info/entry_points.txt,sha256=vO7rX4Fs_xIVJy2pnAtKgTSxfpnozAVQ0DjCmpMxnWE,51
jsonschema-4.25.1.dist-info/licenses/COPYING,sha256=T5KgFaE8TRoEC-8BiqE0MLTxvHO0Gxa7hGw0Z2bedDk,1057
jsonschema/__init__.py,sha256=p-Rw4TS_0OPHZIJyImDWsdWgmd6CPWHMXLq7BuQxTGc,3941
jsonschema/__main__.py,sha256=iLsZf2upUB3ilBKTlMnyK-HHt2Cnnfkwwxi_c6gLvSA,115
jsonschema/__pycache__/__init__.cpython-313.pyc,,
jsonschema/__pycache__/__main__.cpython-313.pyc,,
jsonschema/__pycache__/_format.cpython-313.pyc,,
jsonschema/__pycache__/_keywords.cpython-313.pyc,,
jsonschema/__pycache__/_legacy_keywords.cpython-313.pyc,,
jsonschema/__pycache__/_types.cpython-313.pyc,,
jsonschema/__pycache__/_typing.cpython-313.pyc,,
jsonschema/__pycache__/_utils.cpython-313.pyc,,
jsonschema/__pycache__/cli.cpython-313.pyc,,
jsonschema/__pycache__/exceptions.cpython-313.pyc,,
jsonschema/__pycache__/protocols.cpython-313.pyc,,
jsonschema/__pycache__/validators.cpython-313.pyc,,
jsonschema/_format.py,sha256=ip1N16CBBOb_8sM_iejyW0U5JY9kXoz3O_AFj1SHFl8,15581
jsonschema/_keywords.py,sha256=r8_DrqAfn6QLwQnmXEggveiSU-UaIL2p2nuPINelfFc,14949
jsonschema/_legacy_keywords.py,sha256=2tWuwRPWbYS7EAl8wBIC_rabGuv1J4dfYLqNEPpShhA,15191
jsonschema/_types.py,sha256=0pYJG61cn_4ZWVnqyD24tax2QBMlnSPy0fcECCpASMk,5456
jsonschema/_typing.py,sha256=hFfAEeFJ76LYAl_feuVa0gnHnV9VEq_UhjLJS-7axgY,630
jsonschema/_utils.py,sha256=Xv6_wKKslBJlwyj9-j2c8JDFw-4z4aWFnVe2pX8h7U4,10659
jsonschema/benchmarks/__init__.py,sha256=A0sQrxDBVHSyQ-8ru3L11hMXf3q9gVuB9x_YgHb4R9M,70
jsonschema/benchmarks/__pycache__/__init__.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/const_vs_enum.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/contains.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/issue232.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/json_schema_test_suite.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/nested_schemas.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/subcomponents.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/unused_registry.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/useless_applicator_schemas.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/useless_keywords.cpython-313.pyc,,
jsonschema/benchmarks/__pycache__/validator_creation.cpython-313.pyc,,
jsonschema/benchmarks/const_vs_enum.py,sha256=DVFi3WDqBalZFOibnjpX1uTSr3Rxa2cPgFcowd7Ukrs,830
jsonschema/benchmarks/contains.py,sha256=gexQoUrCOwECofbt19BeosQZ7WFL6PDdkX49DWwBlOg,786
jsonschema/benchmarks/issue232.py,sha256=3LLYLIlBGQnVuyyo2iAv-xky5P6PRFHANx4-zIIQOoE,521
jsonschema/benchmarks/issue232/issue.json,sha256=eaPOZjMRu5u8RpKrsA9uk7ucPZS5tkKG4D_hkOTQ3Hk,117105
jsonschema/benchmarks/json_schema_test_suite.py,sha256=PvfabpUYcF4_7csYDTcTauED8rnFEGYbdY5RqTXD08s,320
jsonschema/benchmarks/nested_schemas.py,sha256=mo07dx-CIgmSOI62CNs4g5xu1FzHklLBpkQoDxWYcKs,1892
jsonschema/benchmarks/subcomponents.py,sha256=fEyiMzsWeK2pd7DEGCuuY-vzGunwhHczRBWEnBRLKIo,1113
jsonschema/benchmarks/unused_registry.py,sha256=hwRwONc9cefPtYzkoX_TYRO3GyUojriv0-YQaK3vnj0,940
jsonschema/benchmarks/useless_applicator_schemas.py,sha256=EVm5-EtOEFoLP_Vt2j4SrCwlx05NhPqNuZQ6LIMP1Dc,3342
jsonschema/benchmarks/useless_keywords.py,sha256=bj_zKr1oVctFlqyZaObCsYTgFjiiNgPzC0hr1Y868mE,867
jsonschema/benchmarks/validator_creation.py,sha256=UkUQlLAnussnr_KdCIdad6xx2pXxQLmYtsXoiirKeWQ,285
jsonschema/cli.py,sha256=av90OtpSxuiko3FAyEHtxpI-NSuX3WMtoQpIx09obJY,8445
jsonschema/exceptions.py,sha256=b42hUDOfPFcprI4ZlNpjDeLKv8k9vOhgWct2jyDlxzk,15256
jsonschema/protocols.py,sha256=lgyryVHqFijSFRAz95ch3VN2Fz-oTFyhj9Obcy5P_CI,7198
jsonschema/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jsonschema/tests/__pycache__/__init__.cpython-313.pyc,,
jsonschema/tests/__pycache__/_suite.cpython-313.pyc,,
jsonschema/tests/__pycache__/fuzz_validate.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_cli.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_deprecations.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_exceptions.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_format.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_jsonschema_test_suite.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_types.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_utils.cpython-313.pyc,,
jsonschema/tests/__pycache__/test_validators.cpython-313.pyc,,
jsonschema/tests/_suite.py,sha256=2k0X91N7dOHhQc5mrYv40OKf1weioj6RMBqWgLT6-PI,8374
jsonschema/tests/fuzz_validate.py,sha256=fUA7yTJIihaCwJplkUehZeyB84HcXEcqtY5oPJXIO7I,1114
jsonschema/tests/test_cli.py,sha256=A89r5LOHy-peLPZA5YDkOaMTWqzQO_w2Tu8WFz_vphM,28544
jsonschema/tests/test_deprecations.py,sha256=yG6mkRJHpTHbWoxpLC5y5H7fk8erGOs8f_9V4tCBEh8,15754
jsonschema/tests/test_exceptions.py,sha256=lWTRyeSeOaFd5dnutqy1YG9uocnxeM_0cIEVG6GgGMI,24310
jsonschema/tests/test_format.py,sha256=eVm5SMaWF2lOPO28bPAwNvkiQvHCQKy-MnuAgEchfEc,3188
jsonschema/tests/test_jsonschema_test_suite.py,sha256=tAfxknM65OR9LyDPHu1pkEaombLgjRLnJ6FPiWPdxjg,8461
jsonschema/tests/test_types.py,sha256=cF51KTDmdsx06MrIc4fXKt0X9fIsVgw5uhT8CamVa8U,6977
jsonschema/tests/test_utils.py,sha256=sao74o1PyYMxBfqweokQN48CFSS6yhJk5FkCfMJ5PsI,4163
jsonschema/tests/test_validators.py,sha256=eiaigsZMzHYYsniQ1UPygaS56a1d-_7-9NC4wVXAhzs,87975
jsonschema/tests/typing/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jsonschema/tests/typing/__pycache__/__init__.cpython-313.pyc,,
jsonschema/tests/typing/__pycache__/test_all_concrete_validators_match_protocol.cpython-313.pyc,,
jsonschema/tests/typing/test_all_concrete_validators_match_protocol.py,sha256=I5XUl5ZYUSZo3APkF10l8Mnfk09oXKqvXWttGGd3sDk,1238
jsonschema/validators.py,sha256=AI0bQrGJpvEH1RSO3ynwWcNvfkqN6WJPgyy0VN7WlSE,47147
