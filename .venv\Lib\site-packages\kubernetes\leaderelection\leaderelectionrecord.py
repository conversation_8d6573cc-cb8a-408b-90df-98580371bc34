# Copyright 2021 The Kubernetes Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


class LeaderElectionRecord:
    # Annotation used in the lock object
    def __init__(self, holder_identity, lease_duration, acquire_time, renew_time):
        self.holder_identity = holder_identity
        self.lease_duration = lease_duration
        self.acquire_time = acquire_time
        self.renew_time = renew_time
